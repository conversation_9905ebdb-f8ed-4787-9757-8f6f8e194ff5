# AI Novel Creator

An AI-assisted novel writing tool designed based on an 80/20 human-AI collaboration model. This project aims to provide writers with intelligent assistance for various aspects of the novel writing process, including character development, plot outlining, world-building, and text generation.

## Project Goal

To create a web-based application that leverages Large Language Models (LLMs), Retrieval Augmented Generation (RAG), and Knowledge Graphs (KG) to enhance the creative writing experience. The tool will focus on providing structured support while allowing the human writer to maintain control and drive the creative direction.

## Current Status (v0.1.0+)

### ✅ **Implemented Features**

*   **🌍 World Building System:**
    *   Complete world setting management interface
    *   Geography, culture, technology/magic system configuration
    *   Keyword and descriptive content management

*   **👥 Character Workshop:**
    *   Dynamic character card creation and management
    *   Comprehensive character profiles (appearance, personality, background, motivation)
    *   Character relationship and arc tracking
    *   Add/remove character functionality

*   **📖 Plot Engine:**
    *   Scene planning and story structure tools
    *   Dynamic scene card management
    *   Multiple story structure template support
    *   Plot point and twist tracking

*   **🤖 AI Integration:**
    *   Google Gemini API integration
    *   AI Sandbox for creative content generation
    *   Context-aware AI assistance
    *   Smart content fill-back to forms

*   **💾 Data Management:**
    *   JSON file storage system
    *   Auto-save functionality
    *   Data loading and synchronization
    *   Completeness checking tools

*   **🎨 User Experience:**
    *   Modern responsive interface
    *   Toast notification system
    *   Tab-based navigation
    *   Loading state indicators
    *   Clean, modular JavaScript architecture

### 🚧 **Planned Features (Future Versions)**

*   **Knowledge Graph Integration:**
    *   Visualize and manage story elements (characters, locations, events, relationships).
    *   Ensure consistency and track complex interdependencies.
*   **Iterative Refinement:**
    *   Allow users to provide feedback to the AI for improved suggestions.
    *   Support multiple iterations of generated content.
*   **Advanced Project Management:**
    *   Chapter organization and management
    *   Export/import functionality
    *   Version control for story elements

## Tech Stack (Initial)

*   **Backend:** Python (Flask)
*   **Frontend:** HTML, CSS, JavaScript (potential for a framework like Vue.js or React later)
*   **AI/ML:** Libraries for LLM interaction (e.g., Langchain, Transformers), RAG implementation, KG management (e.g., Neo4j driver).
*   **Database:** (To be determined based on KG and data storage needs - could be a graph database, NoSQL, or SQL).
*   **Environment Management:** `python-dotenv`

## Setup and Installation

1.  **Clone the repository (if applicable):**
    ```bash
    # git clone <repository-url>
    # cd AI_Novel_Creator
    ```

2.  **Create a virtual environment:**
    ```bash
    python -m venv venv
    ```
    *   On Windows:
        ```bash
        .\venv\Scripts\activate
        ```
    *   On macOS/Linux:
        ```bash
        source venv/bin/activate
        ```

3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

4.  **Set up environment variables:**
    *   Copy `.env.example` to `.env`:
        ```bash
        cp .env.example .env
        ```
    *   Edit the `.env` file and add your actual API keys and configurations:
        ```env
        FLASK_APP=app.py
        FLASK_ENV=development
        SECRET_KEY='generate_a_strong_random_secret_key'

        # Add your API keys here if needed for LLMs, etc.
        # OPENAI_API_KEY='your_openai_api_key'
        ```

## Running the Application

1.  **Ensure your virtual environment is activated.**

2.  **Run the Flask development server:**
    ```bash
    flask run
    ```
    Or, if you prefer to specify host and port (as in `app.py`):
    ```bash
    python app.py
    ```

3.  Open your web browser and navigate to `http://127.0.0.1:5001/` (or the address shown in your terminal).

## Project Structure

```
AI_Novel_Creator/
├── .env.example          # Example environment variables
├── .gitignore            # Files to ignore in Git
├── app.py                # Main Flask application
├── requirements.txt      # Python dependencies
├── README.md             # This file
├── static/               # Static assets (CSS, JS, images)
│   ├── css/
│   │   └── style.css
│   └── js/
│       └── main.js
├── templates/            # HTML templates
│   └── index.html
├── core/                 # Core logic for AI, KG, RAG (to be developed)
│   └── .gitkeep
└── data/                 # Data storage (e.g., KG data, user projects - to be developed)
    └── .gitkeep
```

## Contributing

(Details on how to contribute to the project, if applicable, will be added here.)

## License

(Specify a license for the project, e.g., MIT, Apache 2.0. For now, assume proprietary unless specified.)
