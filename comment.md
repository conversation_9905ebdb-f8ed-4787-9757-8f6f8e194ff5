### 總體評價

*   **優點**: 功能概念清晰，核心邏輯（API 封裝、資料流抽象）設計良好，並已考慮到安全性（`escapeHtml`）和非同步處理（`async/await`）。
*   **核心問題**: **嚴重的程式碼重複與混亂**。存在多個版本的函數實作、重複的事件監聽器和大量被註解的遺留程式碼。這使得維護極為困難，且容易引發錯誤。

---

### 高度優先的優化建議 (High-Impact Recommendations)

以下是您應該最先處理的三個關鍵問題，解決它們將會帶來最大的改善。

#### 1. 【首要任務】消除程式碼重複與混亂

**問題**: 檔案中存在多個 `addCharacterCard` 和 `addSceneCardToForm` 的實作，以及針對同個元素（如 `characterWorkshopForm`）的多個、功能重疊的事件監聽器。

**解決方案**:
1.  **統一函數**: 選擇一個版本的 `addCharacterCard` 和 `addSceneCardToForm` 作為「正式版」，並**徹底刪除**所有其他版本及其註解。
2.  **合併事件監聽器**: 將所有針對同一個父元素（例如 `characterWorkshopForm` 或 `plotEngineForm`）的事件監聽器合併成一個。使用**事件委派**來處理不同子元素的點擊事件。

**範例：合併 `characterWorkshopForm` 的事件監聽器**

```javascript
// --- 刪除所有分散的 characterWorkshopForm.addEventListener ---

// --- 建立一個統一的監聽器 ---
if (characterWorkshopForm) {
    // 初始載入時，populateCharactersForm 會處理卡片生成
    characterWorkshopForm.addEventListener('click', (e) => {
        // 處理「新增角色」按鈕
        if (e.target.classList.contains('add-character-btn')) {
            addCharacterCard(characterWorkshopForm); // 傳遞表單元素作為容器
        }
        // 處理「移除角色」按鈕
        else if (e.target.classList.contains('remove-character-btn')) {
            const cardList = characterWorkshopForm.querySelector('#character-list');
            // 確保至少保留一個卡片
            if (cardList && cardList.querySelectorAll('.character-card').length > 1) {
                e.target.closest('.character-card').remove();
                showToast('角色卡片已移除。', 'info');
            } else {
                showToast('至少需要保留一個角色卡片。', 'warning');
            }
        }
    });
}
```

#### 2. 採用更安全、更高效的 DOM 操作方式

**問題**: `addCharacterCard` 和 `addSceneCardToForm` 使用 `innerHTML` 或 `insertAdjacentHTML` 來生成卡片。這有潛在的 XSS 風險，且效能不如使用 DOM API 或 `<template>` 標籤。

**解決方案**: 使用 `<template>` 標籤預先定義卡片模板，然後在需要時複製並填充它。

**步驟**:
1.  **在 HTML 中定義模板**:
    ```html
    <!-- 放在 HTML 檔案中，例如 </body> 之前 -->
    <template id="character-card-template">
        <div class="character-card card mb-3">
            <div class="card-body">
                <h5 class="card-title">新角色</h5>
                <!-- 使用 data-field 屬性來標識欄位，比解析 id 更健壯 -->
                <div class="mb-3">
                    <label class="form-label">姓名</label>
                    <input type="text" class="form-control" data-field="name" placeholder="輸入角色姓名">
                </div>
                <div class="mb-3">
                    <label class="form-label">描述</label>
                    <textarea class="form-control" data-field="description" rows="3" placeholder="描述角色外貌、性格等"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">角色定位</label>
                    <input type="text" class="form-control" data-field="role" placeholder="主角、配角、反派等">
                </div>
                <button type="button" class="btn btn-danger btn-sm remove-character-btn">移除角色</button>
            </div>
        </div>
    </template>
    ```

2.  **修改 JavaScript 函數**:
    ```javascript
    // 獲取模板
    const characterCardTemplate = document.getElementById('character-card-template');

    function addCharacterCard(container, data = {}) {
        const characterList = container.querySelector('#character-list');
        if (!characterList || !characterCardTemplate) {
            console.error('角色列表容器或模板未找到。');
            return;
        }

        // 複製模板內容
        const cardFragment = characterCardTemplate.content.cloneNode(true);
        const newCard = cardFragment.querySelector('.character-card');

        // 更新卡片標題和 ID (可選)
        characterCount++;
        newCard.id = `character-card-${characterCount}`;
        newCard.querySelector('.card-title').textContent = `角色 ${characterCount}`;

        // 填充資料
        if (data) {
            newCard.querySelector('[data-field="name"]').value = data.name || '';
            newCard.querySelector('[data-field="description"]').value = data.description || '';
            newCard.querySelector('[data-field="role"]').value = data.role || '';
        }

        // 將新卡片加入 DOM
        characterList.appendChild(newCard);
    }
    ```

3.  **對應修改資料收集函數**:
    ```javascript
    function collectCharactersFormData(form) {
        const characters = [];
        form.querySelectorAll('.character-card').forEach(card => {
            const char = {
                // 使用 data-field 屬性來安全、準確地獲取欄位
                name: card.querySelector('[data-field="name"]').value,
                description: card.querySelector('[data-field="description"]').value,
                role: card.querySelector('[data-field="role"]').value,
            };
            characters.push(char);
        });
        return characters;
    }
    ```
    **優點**:
    *   **安全**: 完全避免 XSS 風險。
    *   **高效**: 瀏覽器只需解析一次模板，後續複製操作非常快。
    *   **解耦**: HTML 結構與 JavaScript 邏輯分離，更易維護。
    *   **健壯**: 使用 `data-field` 屬性比解析 `id` 字串更可靠。

#### 3. 建立統一的狀態管理 (State Management)

**問題**: 應用程式的狀態（資料）同時存在於全域變數 (`worldData`, etc.) 和 DOM 表單元素中。這兩者之間的同步依賴手動呼叫，容易出錯並導致資料不一致。

**解決方案**: 將全域變數視為**唯一資料來源 (Single Source of Truth)**。所有 UI 的更新都應該基於這個資料來源來進行渲染。

**重構思路**:
1.  **建立一個主狀態物件**:
    ```javascript
    const appState = {
        world: {},
        characters: [],
        plot: {},
        projectSettings: {}
    };
    ```

2.  **修改資料載入邏輯**: `loadAndPopulateData` 不僅要更新狀態，還應該觸發一個「重新渲染」的過程。
    ```javascript
    async function loadData(dataType, apiUrl) {
        try {
            const data = await fetchData(apiUrl);
            appState[dataType] = data; // 直接更新狀態
            render(dataType); // 觸發渲染
            showToast(`${dataType} 資料載入成功。`, 'success');
        } catch (error) {
            // ...
        }
    }
    ```

3.  **建立渲染函數**:
    ```javascript
    function render(dataType) {
        switch (dataType) {
            case 'world':
                populateWorldForm(appState.world, worldBuildingForm);
                break;
            case 'characters':
                populateCharactersForm(appState.characters, characterWorkshopForm);
                break;
            // ... 其他 case
        }
    }
    ```

4.  **修改使用者互動邏輯**: 使用者操作（例如新增卡片、輸入文字）應該先**更新 `appState`**，然後再**呼叫 `render`** 函數來更新 UI。
    ```javascript
    // 範例：新增角色按鈕的點擊處理
    if (e.target.classList.contains('add-character-btn')) {
        // 1. 更新狀態
        appState.characters.push({ name: '', description: '', role: '' });
        // 2. 重新渲染
        render('characters');
    }
    ```
    這種模式雖然會增加一些複雜度，但它徹底解決了狀態不一致的問題，是所有現代前端框架（如 React, Vue）的核心思想，能讓應用在擴展時保持穩定。

---

### 其他具體優化建議

*   **配置化**: `initialLoad` 和儲存按鈕的 `switch` 語句中有很多重複的設定（dataType, apiUrl, formElement, ...）。可以將它們配置化。
    ```javascript
    const TABS_CONFIG = {
        'world-building': {
            dataType: 'world',
            apiUrl: '/api/world',
            form: worldBuildingForm,
            populateFn: populateWorldForm,
            collectFn: collectWorldFormData,
        },
        'character-workshop': {
            dataType: 'characters',
            apiUrl: '/api/characters',
            form: characterWorkshopForm,
            populateFn: populateCharactersForm,
            collectFn: collectCharactersFormData,
        },
        // ... 其他 tab
    };

    // 儲存按鈕邏輯就可以簡化
    button.addEventListener('click', async (event) => {
        const tabId = event.target.closest('.tab-content').id;
        const config = TABS_CONFIG[tabId];
        if (config) {
            await collectAndSaveData(config.dataType, config.apiUrl, config.form, config.collectFn);
        } else {
            showToast(`未知的儲存目標: ${tabId}`, 'error');
        }
    });
    ```

*   **檔案模組化**: 當程式碼清理完畢後，考慮將其拆分成多個檔案（例如 `api.js`, `ui.js`, `character.js`），並使用 ES6 模組 (`import`/`export`) 來組織，這將極大地提高長期可維護性。

### 結論

這份程式碼是一個非常有潛力的專案原型。當前的首要任務是**重構和清理**，而不是添加新功能。

1.  **立即行動**: 著手**消除重複程式碼**和**合併事件監聽器**。
2.  **中期目標**: 將動態卡片的生成方式改為使用**`<template>` 標籤**。
3.  **長期規劃**: 考慮引入**單一狀態管理**的模式，並將程式碼**模組化**。

完成這些優化後，您將得到一個結構清晰、安全可靠、易於擴展的程式碼庫。