# 主要應用程式檔案 (app.py)
# 負責初始化 Flask 應用程式、載入設定、註冊藍圖等。

import os
from flask import Flask
from dotenv import load_dotenv

# 載入環境變數
load_dotenv()

def create_app():
    """建立並設定 Flask 應用程式實例。"""
    app = Flask(__name__, template_folder='templates', static_folder='static')

    # --- 應用程式設定 ---
    # 從環境變數讀取 SECRET_KEY，若無則使用預設值 (僅供開發)
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'a_very_secret_key_for_development_should_be_changed')
    # 從環境變數讀取 FLASK_DEBUG 狀態
    app.config['DEBUG'] = os.getenv('FLASK_DEBUG', 'False').lower() in ('true', '1', 't')
    # 設定 JSON 檔案儲存路徑 (相對於 AI_Novel_Creator 資料夾)
    app.config['DATA_FOLDER'] = os.path.join(os.path.dirname(__file__), 'data')
    # 確保 data 資料夾存在
    if not os.path.exists(app.config['DATA_FOLDER']):
        os.makedirs(app.config['DATA_FOLDER'])

    # --- 註冊藍圖 (Blueprints) ---
    # 藍圖可以幫助我們將應用程式的不同部分模組化
    # 例如，可以將主要路由、API 路由等放在不同的藍圖中

    # 引入核心路由模組
    from core.routes import main_bp
    app.register_blueprint(main_bp) # 註冊主要功能的藍圖

    # 引入 API 路由模組 (如果未來有專門的 API 路由)
    # from core.api_routes import api_bp # 假設 api_routes.py 存放 API 相關路由
    # app.register_blueprint(api_bp, url_prefix='/api') # API 路由通常會有 /api 前綴

    # --- 錯誤處理 ---
    # 可以在這裡註冊全域的錯誤處理函數
    # 例如：
    # @app.errorhandler(404)
    # def page_not_found(e):
    #     return render_template('404.html'), 404

    # @app.errorhandler(500)
    # def internal_server_error(e):
    #     return render_template('500.html'), 500

    return app

# 主程式進入點
if __name__ == '__main__':
    app = create_app() # 建立應用程式實例
    # 從環境變數讀取 PORT，若無則使用預設值 5001
    port = int(os.getenv('PORT', 5001))
    # 執行應用程式
    # host='0.0.0.0' 讓應用程式可以從外部網路存取 (開發時方便)
    app.run(debug=app.config['DEBUG'], host='0.0.0.0', port=port)