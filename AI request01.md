**强化用户设定环节的深度与灵活性，通过分页签组织大量参数，并建立智能的「设定回填」与「AI整合生成」机制。** 

---

## **核心架构：分层设定 + 动态回填 + 智能整合**
### **界面布局：页签式功能区 + 中央工作区**
1.  **左侧：垂直页签导航栏** (可折叠/展开)
    *   **世界构建** 🌍
    *   **角色工坊** 👤
    *   **情节引擎** 📜
    *   **风格调色板** 🎨
    *   **场景库** 🏰 (可选)
    *   **规则手册** 📖 (核心物理/魔法/社会规则)
2.  **中央：动态编辑面板**
    *   根据所选页签**显示对应的详细参数输入表单**。
    *   提供**预设模板**快速启动（如“奇幻史诗”“都市悬疑”）。
3.  **右侧：AI实时预览区 & 设定回填区** (核心创新！)
    *   **【AI生成沙盒】**：用户可随时将**任意页签的设定项**发送至此，要求AI基于*当前已保存的所有设定*生成该部分的**草稿/示例/扩展** (如：生成“主角首次登场”场景；生成“魔法体系”详细说明)。
    *   **【设定回填按钮】**：将AI沙盒中满意的内容，**一键回填至对应的设定表单**中（如将生成的魔法说明回填到“世界观>魔法体系”文本框）。
4.  **底部：全局控制栏**
    *   **【整合生成按钮】**：基于*所有页签的完整设定*，启动AI生成整部小说初稿。
    *   **【设定完整性检查】**：提示未填写的关键项。

---

## **深度设定页签详解 (用户主导的20%)**
### **1. 世界构建 🌍** (层级化参数)
*   **核心概念：** 一句话主旨 + 关键词 (如 `赛博朋克` `阶级固化` `意识上传` `香港2077`)
*   **物理/地理：**
    *   星球/大陆/城市地图 (可上传草图，AI描述细节)
    *   气候、地貌、关键地点 (带`重要性`权重滑块)
*   **社会结构：**
    *   势力组织 (名称、目标、领袖、标志) + 关系 (敌对/同盟)
    *   文化习俗 (节日、禁忌、礼仪) + 经济体系 (货币、资源)
*   **科技/魔法体系：**
    *   能量来源、使用限制、代价、常见技能/设备 (支持**树状图**或**表格**输入)
    *   `严谨度`滑块 (硬核科幻/低魔幻想 → 软科幻/高魔)
*   **历史事件：** 时间轴工具 (输入关键事件节点，AI填充细节)

### **2. 角色工坊 👤** (支持批量导入/导出)
*   **角色卡片系统：**
    *   **基础档案：** 姓名、年龄、种族/职业、外貌关键词 (`银发` `机械义眼` `疤痕`)
    *   **核心维度：**
        *   目标/欲望 | 恐惧/弱点 | 信念/口头禅
        *   `道德坐标` (九宫格定位) + `关键关系` (下拉菜单选其他角色)
    *   **角色弧光：** 初始状态 → 转折点 → 最终成长 (文本框+关键词)
*   **关系图谱：**
    *   拖拽角色生成关联线，标注关系类型 (亲情/仇恨/合作) + 强度值
    *   AI建议：基于角色设定，生成潜在冲突或盟友关系

### **3. 情节引擎 📜** (骨架+肌肉生成)
*   **故事类型：** 多选标签 (`悬疑` `成长` `复仇` `爱情`) + 基调 (`黑暗` `幽默` `悲壮`)
*   **核心冲突：** 主冲突类型 (人/自然/社会/自我) + 反派动机 (若适用)
*   **三幕结构强化：**
    *   **激励事件** | **转折点1** | **中点高潮** | **转折点2** | **最终决战** (每项可展开填细节)
    *   用户可为每个节点设定**必须发生的事件**或**避免发生的事件**
*   **支线管理：** 添加副线情节 (如爱情线、政治阴谋)，绑定关联角色和影响权重。

### **4. 风格调色板 🎨** (控制AI文风)
*   **叙事视角：** 单选 (第一人称/第三人称有限/上帝视角)
*   **语言风格：** 多维度滑块：
    *   `简洁 <-> 华丽` | `严肃 <-> 诙谐` | `直接 <-> 隐喻`
*   **节奏密度：** 章节长度期望 + 动作戏/文戏比例调节
*   **禁忌词库：** 输入不希望出现的词汇/描写类型 (如 `避免血腥细节` `禁用“突然”`)
*   **范例注入：** 粘贴1-2段用户自己的文字或经典段落，AI学习其风格。

### **5. (可选) 场景库 🏰**
*   预制场景模板 (酒馆谈话、太空战斗、宫廷辩论) + 自定义场景
*   绑定：地点、参与角色、氛围关键词 (`紧张` `浪漫` `诡异`)

### **6. (可选) 规则手册 📖**
*   物理法则、特殊能力限制、社会潜规则条目式输入。
*   **AI一致性检查器来源**，确保生成不违反规则。

---

## **核心工作流：设定 → 回填 → 整合**
1.  **用户逐项填写页签参数** (利用关键词、滑块、模板降低负担)。
2.  **随时使用【AI生成沙盒】测试局部设定：**
    *   选中“角色>主角”卡，点击`生成角色初遇场景` → AI基于*当前世界和风格设定*生成一段文本。
    *   用户若满意，点击`回填至角色档案`，该文本存入角色“背景故事”字段。
3.  **【整合生成】最终稿件：**
    *   AI将**所有页签参数**编译成一个超级结构化提示 (含角色关系图、世界规则树、情节节点)。
    *   采用**分章生成+自动衔接**技术，确保长文连贯性。
    *   输出时**标记AI生成部分**，并附注依据的设定项 (如：`[根据角色卡“约翰”生成对话]`)。

---

## **技术实现关键点**
1.  **结构化数据存储：** 使用JSON Schema或图数据库存储复杂设定关联 (如角色A的敌人是势力B的领袖)。
2.  **动态提示词编译：** 将分散的设定项实时组合成AI可执行的精准指令，例如：
    *   `写一个场景：在[世界设定-地点：霓虹夜市]，[角色：侦探] 与 [角色：线人] 秘密会面，氛围[风格：紧张悬疑]，需揭示[情节：关键线索X]，避免[禁忌：直接暴力描写]`
3.  **回填内容解析：** AI生成文本时自动关联元数据 (如生成“魔法战斗”时，标记使用的世界观规则条目)。
4.  **版本快照：** 每次整合生成前保存设定快照，便于回溯。

---

## **用户价值与优势**
*   **极致控制权：** 用户通过数百项参数精准“编程”AI，避免偏离预期。
*   **灵活迭代：** 沙盒回填机制让设定与内容创作循环优化，用户可边设边看效果。
*   **降低认知负担：** 页签分类管理海量设定，AI辅助填充细节文本。
*   **高效整合：** 告别碎片化设定文档，一键生成结构严谨的初稿。
