# Python
__pycache__/
*.py[cod]
*$py.class

# Environments
.env
.venv
venv/
ENV/
env/

# IDE specific files
.idea/
.vscode/

# Operating System files
.DS_Store
Thumbs.db

# Log files
*.log

# Data files (if they are large or sensitive)
# data/*.json
# data/*.db

# Compiled files
*.so
*.o

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a script, but you might want to ignore them
#  even if the generated files are part of your project itself.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
.pytest_cache/
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/