# core/utils.py
# 這個檔案包含一些通用的輔助函數，例如檔案讀寫、資料驗證等。

import json
import os
from flask import current_app # 用於獲取當前應用程式的上下文，例如設定

def load_data(file_path, default_data=None):
    """從指定的 JSON 檔案載入資料。

    Args:
        file_path (str): JSON 檔案的路徑。
        default_data (any, optional): 如果檔案不存在或為空，則返回的預設資料。
                                      預設為 None。

    Returns:
        any: 從檔案中載入的資料，或在發生錯誤/檔案不存在時返回 default_data。
    """
    try:
        # 檢查檔案是否存在
        if not os.path.exists(file_path):
            # 如果檔案不存在且提供了預設資料，則可以選擇建立一個包含預設資料的檔案
            if default_data is not None:
                # 確保檔案所在目錄存在
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(default_data, f, ensure_ascii=False, indent=4)
                current_app.logger.info(f"檔案 {file_path} 不存在，已使用預設資料建立。")
                return default_data
            else:
                current_app.logger.warning(f"嘗試載入的檔案 {file_path} 不存在，且未提供預設資料。")
                return default_data # 或者可以引發一個自訂錯誤
        
        # 讀取檔案內容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
            if not content: # 檢查檔案是否為空
                current_app.logger.warning(f"檔案 {file_path} 為空，返回預設資料。")
                # 如果檔案為空且提供了預設資料，可以選擇用預設資料覆寫它
                if default_data is not None:
                    with open(file_path, 'w', encoding='utf-8') as fw:
                        json.dump(default_data, fw, ensure_ascii=False, indent=4)
                    return default_data
                return default_data
            data = json.loads(content)
            return data
    except FileNotFoundError:
        current_app.logger.error(f"檔案 {file_path} 未找到。")
        return default_data
    except json.JSONDecodeError:
        current_app.logger.error(f"解碼 JSON 檔案 {file_path} 失敗。檔案可能已損毀或格式不正確。")
        # 可以考慮備份損毀的檔案並使用預設資料
        return default_data
    except Exception as e:
        # 捕獲其他可能的錯誤，例如權限問題
        current_app.logger.error(f"載入檔案 {file_path} 時發生未預期的錯誤: {e}")
        return default_data

def save_data(file_path, data):
    """將資料儲存到指定的 JSON 檔案。

    Args:
        file_path (str): 要儲存資料的 JSON 檔案路徑。
        data (any): 要儲存的資料 (應為可序列化為 JSON 的 Python 物件)。

    Returns:
        bool: 如果儲存成功則返回 True，否則返回 False。
    """
    try:
        # 確保檔案所在目錄存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        # 以寫入模式開啟檔案，並使用 utf-8 編碼
        # ensure_ascii=False 確保中文字元能正確儲存而不是被轉義
        # indent=4 使 JSON 檔案更具可讀性
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
        current_app.logger.info(f"資料已成功儲存到 {file_path}")
        return True
    except TypeError as e:
        # 如果資料無法序列化為 JSON
        current_app.logger.error(f"儲存資料到 {file_path} 失敗：資料無法序列化為 JSON。錯誤: {e}")
        return False
    except Exception as e:
        # 捕獲其他可能的錯誤，例如權限問題
        current_app.logger.error(f"儲存資料到 {file_path} 時發生未預期的錯誤: {e}")
        return False

# 可以在此處添加更多輔助函數，例如：
# - 資料驗證函數 (例如使用 Pydantic 或 Marshmallow)
# - 字串處理函數
# - 日期時間處理函數