# AI小說創作輔助平台（多參數設定＋頁籤介面）專案說明書

---

## 一、產品定位

一款以AI為主力、用戶深度參與的小說創作工具。用戶可在創作前透過多維度參數化設定（如世界觀、角色、章節架構、文風等40+欄位），每一項皆可交由AI生成與擴寫。所有設定區塊採「頁籤切換」介面，並有共用AI生成區，能將AI回填至任一設定項，最終AI會自動整合所有設定，產出全書章節初稿，作者僅需審稿修訂。

---

## 二、核心功能架構

### 1. 設定區（多頁籤介面）

* 各分類獨立分頁（如：世界觀、種族、主題、故事結構、角色卡、配角群、章節節奏、科技/魔法系統、語言、專有名詞等）
* 每頁有可編輯欄位（表單、下拉選單、關鍵字Tag、長文本等），部分欄位可批次上傳。
* 欄位右側可一鍵呼叫AI生成、擴寫或優化內容，亦可回填用戶自訂。

### 2. 共用AI生成區

* 固定區塊（浮動或主畫面右側），用戶可複製任何欄位內容至AI生成區，由AI產生回覆後一鍵回填至指定欄位。
* 支援多種提示模板（世界觀/角色/情節/專有名詞/主題等，隨使用場景自動切換）。
* 支援連結外部資料（如Wiki、草稿片段）進行RAG增強。

### 3. 設定整併與初稿生成

* 完成所有欄位設定後，用戶點擊「生成全書」或「產生章節草稿」。
* 系統自動彙整所有用戶及AI內容，進行章節大綱生成、細節擴寫。
* 初稿生成過程採「流水式/Skeleton loading」顯示。
* 每一章節皆可一鍵展開，查看參考設定來源、AI擴寫內容、用戶修改歷程。

### 4. 編輯與審稿

* 採用Tiptap協作編輯器，支援多人審稿、即時編輯、評論、版本控制。
* 內建AI建議修訂（語氣一致、文法優化、角色語調等）。
* 支援導出EPUB/DOCX/KDP格式，匯出時自動加入AI參與說明頁。

---

## 三、設定欄位（範例，實際可拓展40+）

| 分類   | 欄位範例                     | 用途/AI任務       |
| ---- | ------------------------ | ------------- |
| 世界觀  | 年代、地理、社會體制、禁忌            | 生成World Bible |
| 種族   | 種族名、特性、壽命、文化、衝突          | 擴寫文化/語言/傳說    |
| 角色卡  | 姓名、年齡、性格、缺陷、弧線目標         | 擴寫背景故事/語氣樣本   |
| 章節結構 | 故事模板、情節節點、劇情伏筆           | 產生章節骨架/伏筆追蹤   |
| 文風   | 作者參考、語料鏈結、敘事視角           | 文體對標/語氣模擬     |
| 主題   | 主題、反命題、核心訊息              | 產生對照表         |
| 內容分級 | 分級、敏感詞、暴力/情色/禁區          | 過濾敏感/合規內容     |
| 關鍵詞  | 人名、地名、專有名詞（可批次上傳）        | 保證一致/向量索引     |
| 讀者目標 | 年齡層、文化圈、載具               | 字數、難度自動配適     |
| 輸入素材 | 大綱、wiki、片段、圖片（moodboard） | RAG檢索增強       |

---

## 四、介面設計重點

* 採用Material UI/Tailwind，風格簡潔現代，左側頁籤、主區設定、右側AI生成區。
* 表單欄位依不同分類自動切換欄位類型，提供即時Token估算、內容預覽。
* AI生成區支援Prompt微調、回填、產生紀錄比對。
* 支援協作/批次操作（多人填表、角色表批次導入、欄位複製）

---

## 五、技術架構與資料流

* 前端：Next.js + React + Tiptap協作編輯 + Material UI/Tailwind
* 後端：FastAPI/Node.js + LangChain/LlamaIndex + Supabase Vector/ChromaDB
* AI模型：GPT-4o（可多模型擴充），RAG增強
* 全站採JSON Schema 驗證設定資料（zod/yup）

### 資料流

1. 用戶填寫/AI擴寫所有設定欄位 →
2. 共用AI生成區產生內容/回填 →
3. 完成設定一鍵整併 →
4. 系統呼叫AI批次生成章節初稿 →
5. 編輯/審稿/修訂 →
6. 導出/發佈

---

## 六、合規與後續擴充

* 匯出時自動產生AI參與說明，支援KDP/Medium/出版要求
* 可擴充語音敘事、圖像分析產生關鍵詞、角色對話模擬等功能
* 支援API串接、Webhook存檔、成本統計

---
